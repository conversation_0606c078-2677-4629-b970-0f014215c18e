{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node src/server.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/multer": "^1.4.12", "bcrypt": "^5.1.1", "better-sqlite3": "^11.9.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "express": "^4.21.2", "fs": "^0.0.1-security", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemon": "^3.1.9", "path": "^0.12.7", "reflect-metadata": "^0.2.2", "sqlite3": "^5.1.7", "typeorm": "^0.3.20"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.5", "@types/sequelize": "^4.28.20", "@types/sqlite3": "^3.1.11", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}